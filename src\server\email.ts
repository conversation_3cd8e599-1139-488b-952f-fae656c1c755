"use server";

import nodemailer from "nodemailer";
import dns from "dns";

type SendEmailArgs = {
  to: string;
  subject: string;
  text: string;
  secure?: boolean;
};

export const sendEmail = async (toOrArgs: string | SendEmailArgs, subject?: string, text?: string, secure: boolean = false) => {
  // normalize args: support sendEmail(to, subject, text) or sendEmail({ to, subject, text })
  const args: SendEmailArgs = typeof toOrArgs === "string" ? { to: toOrArgs, subject: subject || "", text: text || "", secure } : toOrArgs;

  if (!process.env.SMTP_HOST) {
    throw new Error("Missing SMTP_HOST environment variable. Set SMTP_HOST to your SMTP server (e.g. smtp.gmail.com or mailtrap.io)");
  }

  if (!process.env.SMTP_USER || !process.env.SMTP_PASS) {
    throw new Error("Missing SMTP_USER or SMTP_PASS environment variables. Provide SMTP credentials.");
  }

  // Quick DNS lookup to provide a clearer error when a hostname like "mail" doesn't resolve
  try {
    await dns.promises.lookup(process.env.SMTP_HOST!);
  } catch (err: any) {
    const hint =
      `Failed to resolve SMTP_HOST "${process.env.SMTP_HOST}": ${err.code || err.message}.\n` +
      "Possible fixes:\n" +
      '- If you use a Docker service name like "mail", ensure this code runs inside the same Docker network.\n' +
      "- When running on the host and the SMTP server is in a container, try using host.docker.internal (Windows/Mac) or expose the SMTP port.\n" +
      "- Use a public SMTP provider hostname (smtp.gmail.com, smtp.mailtrap.io) or an IP address.\n" +
      "- Check your DNS / hosts file for misconfiguration.";
    const e = new Error(hint);
    // attach original for debugging
    // @ts-ignore
    e.original = err;
    throw e;
  }

  const transporter = nodemailer.createTransport({
    host: process.env.SMTP_HOST,
    port: Number(process.env.SMTP_PORT) || (secure ? 465 : 587),
    secure: secure,
    auth: {
      user: process.env.SMTP_USER,
      pass: process.env.SMTP_PASS,
    },
    // dev-friendly TLS and timeouts; remove or tighten for production
    tls: { rejectUnauthorized: false },
    connectionTimeout: 10_000,
  });

  try {
    await transporter.sendMail({
      from: `"My App" <${process.env.SMTP_USER}@${process.env.MAIL_DOMAIN}>`,
      to: args.to,
      subject: args.subject,
      text: args.text,
    });
  } catch (err: any) {
    const message = `Failed to send email via host "${process.env.SMTP_HOST}": ${err.code || err.message}. Check SMTP host, port, credentials and network connectivity.`;
    const e = new Error(message);
    // @ts-ignore
    e.original = err;
    throw e;
  }
};
