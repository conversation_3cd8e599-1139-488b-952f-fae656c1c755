services:
  postgres:
    image: postgres:latest
    container_name: game_postgres
    restart: unless-stopped
    ports:
      - "${DATABASE_PORT}:5432"
    environment:
      POSTGRES_USER: ${DATABASE_USER}
      POSTGRES_PASSWORD: ${DATABASE_PASSWORD}
      POSTGRES_DB: ${DATABASE_DB}
    volumes:
      - postgres_data:/var/lib/postgresql/data

  redis:
    image: redis:latest
    container_name: game_redis
    restart: unless-stopped
    ports:
      - "${REDIS_PORT}:6379"
    volumes:
      - redis_data:/data

  minio:
    image: minio/minio
    container_name: game_minio
    restart: unless-stopped
    ports:
      - "${S3_PORT}:9000"
      - "${S3_CONSOLE_PORT}:9001"
    environment:
      MINIO_ROOT_USER: ${S3_ROOT_USER}
      MINIO_ROOT_PASSWORD: ${S3_ROOT_PASSWORD}
    command: server /data --console-address ":${S3_CONSOLE_PORT}"
    volumes:
      - minio_data:/data

  mail:
    image: mailu/postfix:2.0
    container_name: game_mail
    restart: unless-stopped
    environment:
      - DOMAIN=${MAIL_DOMAIN}
      - HOSTNAME=${MAIL_HOSTNAME}
      - RELAYNETS=0.0.0.0/0
      - SMTP_USER=${MAIL_USER}
      - SMTP_PASSWORD=${MAIL_PASS}
    ports:
      - "${MAIL_PORT}:25"

volumes:
  postgres_data:
  redis_data:
  minio_data:
