import { betterAuth } from "better-auth";
import { drizzleAdapter } from "better-auth/adapters/drizzle";
import { db } from "@/db";
import * as schema from "@/db/schema";
import { redis } from "./redis";

export const auth = betterAuth({
  database: drizzleAdapter(db, {
    provider: "pg",
    schema: schema,
  }),
  user: {
    changeEmail: {
      enabled: true,
    },
    sendChangeEmailVerification: async () => {},
    sendResetPassword: async () => {},
    sendVerification: async () => {},
  },
  secret: process.env.BETTER_AUTH_SECRET as string,
  baseURL: process.env.BETTER_AUTH_URL as string,
  emailAndPassword: {
    enabled: true,
    requireEmailVerification: false,
    minPasswordLength: 8,
    maxPasswordLength: 128,
  },
  session: {
    expiresIn: 60 * 60 * 24 * 7, // 7 days
    updateAge: 60 * 60 * 24, // 1 day
    cookieCache: {
      enabled: true,
      maxAge: 60 * 5, // 5 minutes
    },
  },
  socialProviders: {
    github: {
      clientId: process.env.GITHUB_CLIENT_ID as string,
      clientSecret: process.env.GITHUB_CLIENT_SECRET as string,
    },
    google: {
      clientId: process.env.GOOGLE_CLIENT_ID as string,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET as string,
    },
  },
  secondaryStorage: {
    get: async (key) => {
      try {
        return await redis.get(key);
      } catch (error) {
        console.error("Redis get error:", error);
        return null;
      }
    },
    set: async (key, value, ttl) => {
      try {
        if (ttl) await redis.set(key, value, { EX: ttl });
        else await redis.set(key, value);
      } catch (error) {
        console.error("Redis set error:", error);
      }
    },
    delete: async (key) => {
      try {
        await redis.del(key);
      } catch (error) {
        console.error("Redis delete error:", error);
      }
    },
  },
});
